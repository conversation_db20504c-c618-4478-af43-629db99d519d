import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import requests
import os
from datetime import datetime
import logging
import webbrowser
from collections import defaultdict
import re
import base64
import zlib
import pyperclip
from PIL import Image, ImageTk

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("augment_manager.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AugmentAccountManager")

class AugmentAccountManager:
    PLAN_CACHE = {}
    BASE_URL = "https://app.augmentcode.com"
    
    def __init__(self, storage_file="accounts.json"):
        self.storage_file = storage_file
        self.accounts = self._load_accounts()
        self.current_account = None
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json",
            "Referer": f"{self.BASE_URL}/account/subscription"
        })
        
        # 预加载计划信息
        self._load_plans()
    
    def _load_plans(self):
        try:
            response = self.session.get(f"{self.BASE_URL}/api/plans", timeout=10)
            if response.status_code == 200:
                self.PLAN_CACHE = {plan['id']: plan for plan in response.json()}
                logger.info(f"成功加载 {len(self.PLAN_CACHE)} 个计划信息")
        except Exception as e:
            logger.error(f"加载计划信息失败: {str(e)}")
    
    def _get_headers(self, session):
        return {
            "Cookie": f"_session={session}",
            "X-Requested-With": "XMLHttpRequest"
        }
    
    def _get_account_details(self, session):
        endpoints = {
            "user": "/api/user",
            "subscription": "/api/subscription",
            "credits": "/api/credits",
            "plan_change": "/api/team/plan-change-pending"
        }
        
        results = {}
        for key, endpoint in endpoints.items():
            try:
                response = self.session.get(
                    f"{self.BASE_URL}{endpoint}",
                    headers=self._get_headers(session),
                    timeout=8
                )
                if response.status_code == 200:
                    results[key] = response.json()
                else:
                    results[key] = {"error": f"状态码 {response.status_code}"}
            except Exception as e:
                results[key] = {"error": str(e)}
                logger.error(f"请求 {endpoint} 失败: {str(e)}")
        
        return results
    
    def _parse_account_data(self, session, data):
        user_data = data.get("user", {})
        sub_data = data.get("subscription", {})
        credit_data = data.get("credits", {})
        
        plan_id = sub_data.get("planId", "unknown")
        plan_info = self.PLAN_CACHE.get(plan_id, {})
        
        total_quota = sub_data.get("creditsIncludedThisBillingCycle", 
                                  plan_info.get("agentRequests", 0))
        used_quota = credit_data.get("usageUnitsUsedThisBillingCycle", 0)
        remaining_quota = credit_data.get("usageUnitsAvailable", 
                                        total_quota - used_quota)
        
        plan_is_expired = sub_data.get("planIsExpired", False)
        billing_end = sub_data.get("billingPeriodEnd")
        trial_end = sub_data.get("trialPeriodEnd")
        
        def format_date(date_str):
            if not date_str:
                return "N/A"
            try:
                dt = datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
                return dt.strftime("%Y-%m-%d %H:%M")
            except:
                return date_str
        
        return {
            "session": session,
            "email": user_data.get("email", "<EMAIL>"),
            "plan_id": plan_id,
            "plan_name": plan_info.get("name", sub_data.get("planName", "Unknown Plan")),
            "plan_type": plan_info.get("planType", "unknown"),
            "quota": {
                "total": total_quota,
                "used": used_quota,
                "remaining": remaining_quota,
                "unit": plan_info.get("usageUnitDisplayName", "user messages")
            },
            "billing": {
                "period_end": format_date(billing_end),
                "trial_end": format_date(trial_end),
                "is_expired": plan_is_expired,
                "monthly_cost": sub_data.get("monthlyTotalCost", "0.00"),
                "additional_cost": sub_data.get("additionalUsageUnitCost", "0.00")
            },
            "features": {
                "has_teams": plan_info.get("hasTeams", False),
                "has_training": plan_info.get("hasTraining", False),
                "max_seats": sub_data.get("maxNumSeats", 1),
                "current_seats": sub_data.get("numberOfSeatsThisBillingCycle", 1)
            },
            "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _load_accounts(self):
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    accounts = json.load(f)
                    logger.info(f"从 {self.storage_file} 加载了 {len(accounts)} 个账号")
                    return accounts
            return []
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_accounts(self):
        with open(self.storage_file, 'w', encoding='utf-8') as f:
            json.dump(self.accounts, f, ensure_ascii=False, indent=2)
            logger.info(f"账号数据已保存至 {self.storage_file}")
    
    def add_account(self, session, notes=""):
        for acc in self.accounts:
            if acc["session"] == session:
                logger.warning("该session已存在，无需重复添加")
                return False
        
        logger.info("正在获取账号信息...")
        api_data = self._get_account_details(session)
        account = self._parse_account_data(session, api_data)
        account["notes"] = notes
        account["added_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.accounts.append(account)
        self._save_accounts()
        logger.info(f"已添加账号: {account['email']}")
        return account
    
    def refresh_account(self, email):
        for i, account in enumerate(self.accounts):
            if account["email"] == email:
                logger.info(f"正在刷新账号: {email}")
                api_data = self._get_account_details(account["session"])
                self.accounts[i] = self._parse_account_data(account["session"], api_data)
                self.accounts[i]["notes"] = account.get("notes", "")
                self.accounts[i]["added_time"] = account.get("added_time", "")
                self._save_accounts()
                logger.info(f"账号 {email} 刷新完成")
                return self.accounts[i]
        logger.warning(f"未找到账号: {email}")
        return None
    
    def refresh_all_accounts(self):
        logger.info("开始刷新所有账号信息...")
        for i in range(len(self.accounts)):
            try:
                account = self.accounts[i]
                logger.info(f"刷新账号 ({i+1}/{len(self.accounts)}): {account['email']}")
                api_data = self._get_account_details(account["session"])
                self.accounts[i] = self._parse_account_data(account["session"], api_data)
                self.accounts[i]["notes"] = account.get("notes", "")
                self.accounts[i]["added_time"] = account.get("added_time", "")
            except Exception as e:
                logger.error(f"刷新账号 {account['email']} 失败: {str(e)}")
        
        self._save_accounts()
        logger.info("所有账号刷新完成")
        return self.accounts
    
    def remove_account(self, email):
        original_count = len(self.accounts)
        self.accounts = [acc for acc in self.accounts if acc["email"] != email]
        if len(self.accounts) < original_count:
            self._save_accounts()
            logger.info(f"已移除账号: {email}")
            return True
        logger.warning(f"未找到账号: {email}")
        return False
    
    def switch_account(self, email):
        for account in self.accounts:
            if account["email"] == email:
                self.current_account = account
                logger.info(f"已切换到账号: {email}")
                return account["session"]
        logger.warning(f"切换失败，未找到账号: {email}")
        return None
    
    def get_account(self, email):
        for account in self.accounts:
            if account["email"] == email:
                return account
        return None
    
    def get_all_accounts(self):
        return self.accounts
    
    def get_quota_summary(self):
        summary = {
            "total_accounts": len(self.accounts),
            "total_quota": 0,
            "total_used": 0,
            "total_remaining": 0,
            "plans": defaultdict(lambda: {"count": 0, "quota": 0})
        }
        
        for account in self.accounts:
            summary["total_quota"] += account["quota"]["total"]
            summary["total_used"] += account["quota"]["used"]
            summary["total_remaining"] += account["quota"]["remaining"]
            
            plan_name = account["plan_name"]
            summary["plans"][plan_name]["count"] += 1
            summary["plans"][plan_name]["quota"] += account["quota"]["remaining"]
        
        summary["plans"] = dict(summary["plans"])
        return summary
    
    def export_accounts(self, filename="augment_backup.json"):
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, ensure_ascii=False, indent=2)
            logger.info(f"账号已导出至 {filename}")
            return True
        except Exception as e:
            logger.error(f"导出失败: {str(e)}")
            return False
    
    def import_accounts(self, filename="augment_backup.json"):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                new_accounts = json.load(f)
            
            existing_emails = {acc["email"] for acc in self.accounts}
            added_count = 0
            
            for acc in new_accounts:
                if acc["email"] not in existing_emails:
                    self.accounts.append(acc)
                    added_count += 1
            
            self._save_accounts()
            logger.info(f"从 {filename} 导入 {added_count} 个新账号")
            return added_count
        except Exception as e:
            logger.error(f"导入失败: {str(e)}")
            return 0


class AugmentManagerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Augment 账号管理器")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # 初始化账号管理器
        self.manager = AugmentAccountManager()
        
        # 创建样式
        self.create_styles()
        
        # 创建UI
        self.create_widgets()
        
        # 启动定时刷新
        self.auto_refresh_interval = 300  # 5分钟
        self.start_auto_refresh()
        
        # 加载账号数据
        self.refresh_account_list()
    
    def create_styles(self):
        # 创建自定义样式
        style = ttk.Style()
        style.configure("TButton", padding=6, font=('Arial', 10))
        style.configure("Header.TLabel", font=('Arial', 14, 'bold'), foreground="#333")
        style.configure("Account.TFrame", background="#f0f0f0", borderwidth=1, relief="groove")
        style.configure("Summary.TFrame", background="#e9f7fe", borderwidth=1, relief="groove")
        style.configure("Treeview", font=('Arial', 10), rowheight=25)
        style.configure("Treeview.Heading", font=('Arial', 10, 'bold'))
        style.map("Treeview", background=[('selected', '#4a6987')], foreground=[('selected', 'white')])
    
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 账号列表
        left_panel = ttk.Frame(main_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, padx=(0, 10), pady=5, expand=False)
        
        # 账号列表标题
        ttk.Label(left_panel, text="账号列表", style="Header.TLabel").pack(anchor=tk.W, pady=(0, 5))
        
        # 账号列表表格
        columns = ("#", "邮箱", "计划", "剩余额度", "状态")
        self.account_tree = ttk.Treeview(
            left_panel, 
            columns=columns, 
            show="headings", 
            selectmode="browse",
            height=15
        )
        
        # 设置列宽
        self.account_tree.column("#", width=40, anchor=tk.CENTER)
        self.account_tree.column("邮箱", width=200, anchor=tk.W)
        self.account_tree.column("计划", width=150, anchor=tk.W)
        self.account_tree.column("剩余额度", width=100, anchor=tk.CENTER)
        self.account_tree.column("状态", width=80, anchor=tk.CENTER)
        
        # 设置列标题
        for col in columns:
            self.account_tree.heading(col, text=col)
        
        self.account_tree.pack(fill=tk.BOTH, expand=True)
        
        # 绑定选择事件
        self.account_tree.bind("<<TreeviewSelect>>", self.on_account_select)
        
        # 账号操作按钮
        btn_frame = ttk.Frame(left_panel)
        btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(btn_frame, text="添加账号", command=self.add_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="刷新账号", command=self.refresh_selected_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除账号", command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="全部刷新", command=self.refresh_all_accounts).pack(side=tk.LEFT, padx=5)
        
        # 账号详情面板
        detail_frame = ttk.LabelFrame(main_frame, text="账号详情")
        detail_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, pady=5)
        
        # 账号详情内容
        self.detail_text = scrolledtext.ScrolledText(
            detail_frame, 
            wrap=tk.WORD, 
            font=('Arial', 10),
            height=15
        )
        self.detail_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.detail_text.config(state=tk.DISABLED)
        
        # 账号操作按钮
        action_frame = ttk.Frame(detail_frame)
        action_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(action_frame, text="复制 Session", command=self.copy_session).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="应用到 VSCode", command=self.apply_to_vscode).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="导出账号", command=self.export_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="导入账号", command=self.import_accounts).pack(side=tk.LEFT, padx=5)
        
        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 9)).pack(side=tk.LEFT)
        
        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            status_frame, 
            text="自动刷新 (5分钟)", 
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh
        ).pack(side=tk.RIGHT, padx=10)
    
    def refresh_account_list(self):
        # 清空现有列表
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)
        
        # 添加账号到列表
        accounts = self.manager.get_all_accounts()
        for i, acc in enumerate(accounts):
            status = "正常" if not acc["billing"]["is_expired"] else "已过期"
            remaining = f"{acc['quota']['remaining']}/{acc['quota']['total']}"
            
            # 根据剩余额度设置标签颜色
            tags = ()
            if acc["quota"]["remaining"] == 0:
                tags = ("expired",)
            elif acc["quota"]["remaining"] < acc["quota"]["total"] * 0.2:
                tags = ("low",)
            
            self.account_tree.insert(
                "", 
                tk.END, 
                values=(
                    i+1, 
                    acc["email"], 
                    acc["plan_name"], 
                    remaining, 
                    status
                ),
                tags=tags
            )
        
        # 配置标签样式
        self.account_tree.tag_configure("expired", background="#ffdddd")
        self.account_tree.tag_configure("low", background="#fff3cd")
        
        # 更新状态栏
        self.update_summary()
    
    def update_summary(self):
        summary = self.manager.get_quota_summary()
        status_text = f"账号总数: {summary['total_accounts']} | "
        status_text += f"总剩余额度: {summary['total_remaining']} | "
        
        # 添加计划信息
        for plan, data in summary["plans"].items():
            status_text += f"{plan}: {data['count']}个账号, "
        
        self.status_var.set(status_text[:-2])  # 去掉最后的逗号和空格
    
    def on_account_select(self, event):
        selected = self.account_tree.selection()
        if not selected:
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if account:
            self.show_account_details(account)
    
    def show_account_details(self, account):
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        
        details = f"邮箱: {account['email']}\n"
        details += f"计划: {account['plan_name']} ({account['plan_type']})\n"
        details += f"额度: {account['quota']['used']}/{account['quota']['total']} {account['quota']['unit']}\n"
        details += f"剩余: {account['quota']['remaining']} {account['quota']['unit']}\n"
        details += f"账单周期结束: {account['billing']['period_end']}\n"
        details += f"试用结束: {account['billing']['trial_end']}\n"
        details += f"状态: {'已过期' if account['billing']['is_expired'] else '正常'}\n"
        details += f"团队功能: {'支持' if account['features']['has_teams'] else '不支持'}\n"
        details += f"训练功能: {'支持' if account['features']['has_training'] else '不支持'}\n"
        details += f"最大席位: {account['features']['max_seats']}\n"
        details += f"当前席位: {account['features']['current_seats']}\n"
        details += f"添加时间: {account.get('added_time', '未知')}\n"
        details += f"最后更新: {account['last_updated']}\n"
        details += f"备注: {account.get('notes', '无')}\n"
        
        self.detail_text.insert(tk.END, details)
        self.detail_text.config(state=tk.DISABLED)
    
    def add_account(self):
        # 创建添加账号对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("添加新账号")
        dialog.geometry("500x300")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建表单
        form_frame = ttk.Frame(dialog, padding=10)
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(form_frame, text="Session 值:").grid(row=0, column=0, sticky=tk.W, pady=5)
        session_entry = scrolledtext.ScrolledText(form_frame, height=5, width=50)
        session_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(0, 10))
        
        ttk.Label(form_frame, text="备注:").grid(row=1, column=0, sticky=tk.W, pady=5)
        notes_entry = ttk.Entry(form_frame, width=50)
        notes_entry.grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(0, 10))
        
        # 按钮框架
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def on_submit():
            session = session_entry.get("1.0", tk.END).strip()
            notes = notes_entry.get().strip()
            
            if not session:
                messagebox.showerror("错误", "Session 值不能为空")
                return
            
            try:
                self.status_var.set("正在添加账号...")
                account = self.manager.add_account(session, notes)
                if account:
                    self.refresh_account_list()
                    dialog.destroy()
                    self.status_var.set(f"账号添加成功: {account['email']}")
                else:
                    self.status_var.set("账号添加失败")
            except Exception as e:
                messagebox.showerror("错误", f"添加账号失败: {str(e)}")
                self.status_var.set(f"添加账号失败: {str(e)}")
        
        ttk.Button(btn_frame, text="添加", command=on_submit).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
    
    def refresh_selected_account(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        
        self.status_var.set(f"正在刷新账号: {email}...")
        self.root.update()
        
        try:
            # 在后台线程中刷新账号
            def refresh_task():
                account = self.manager.refresh_account(email)
                if account:
                    self.refresh_account_list()
                    self.show_account_details(account)
                    self.status_var.set(f"账号刷新成功: {email}")
                else:
                    self.status_var.set(f"账号刷新失败: {email}")
            
            threading.Thread(target=refresh_task, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"刷新账号失败: {str(e)}")
            self.status_var.set(f"刷新失败: {str(e)}")
    
    def refresh_all_accounts(self):
        if not self.manager.get_all_accounts():
            messagebox.showinfo("提示", "没有可刷新的账号")
            return
        
        self.status_var.set("正在刷新所有账号...")
        self.root.update()
        
        try:
            # 在后台线程中刷新所有账号
            def refresh_task():
                self.manager.refresh_all_accounts()
                self.refresh_account_list()
                self.status_var.set("所有账号刷新完成")
            
            threading.Thread(target=refresh_task, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"刷新账号失败: {str(e)}")
            self.status_var.set(f"刷新失败: {str(e)}")
    
    def delete_account(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        
        if messagebox.askyesno("确认", f"确定要删除账号 {email} 吗？"):
            if self.manager.remove_account(email):
                self.refresh_account_list()
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.config(state=tk.DISABLED)
                self.status_var.set(f"已删除账号: {email}")
            else:
                self.status_var.set(f"删除账号失败: {email}")
    
    def copy_session(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if account:
            pyperclip.copy(account["session"])
            self.status_var.set(f"已复制 {email} 的 Session 到剪贴板")
        else:
            self.status_var.set("复制 Session 失败")
    
    def apply_to_vscode(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if account:
            self.status_var.set(f"正在将账号 {email} 应用到 VSCode...")
            
            # 在实际应用中，这里会实现将session应用到VSCode插件的逻辑
            # 目前我们只是模拟操作并显示提示信息
            messagebox.showinfo("操作成功", 
                f"账号 {email} 已成功应用到 VSCode 插件\n\n"
                "在实际应用中，这里会执行以下操作：\n"
                "1. 关闭 VSCode\n"
                "2. 更新插件配置文件\n"
                "3. 重新启动 VSCode"
            )
            self.status_var.set(f"账号 {email} 已应用到 VSCode")
        else:
            self.status_var.set("应用账号失败")
    
    def export_accounts(self):
        filename = "augment_accounts_backup.json"
        if self.manager.export_accounts(filename):
            messagebox.showinfo("导出成功", f"账号已成功导出到文件: {filename}")
        else:
            messagebox.showerror("导出失败", "导出账号时发生错误")
    
    def import_accounts(self):
        # 在实际应用中，这里应该有一个文件选择对话框
        filename = "augment_accounts_backup.json"
        
        if not os.path.exists(filename):
            messagebox.showerror("错误", f"文件 {filename} 不存在")
            return
        
        count = self.manager.import_accounts(filename)
        if count > 0:
            self.refresh_account_list()
            messagebox.showinfo("导入成功", f"成功导入 {count} 个账号")
        else:
            messagebox.showinfo("导入完成", "没有新账号可导入")
    
    def start_auto_refresh(self):
        def auto_refresh_task():
            while True:
                if self.auto_refresh_var.get():
                    try:
                        # 每5分钟自动刷新一次
                        time.sleep(self.auto_refresh_interval)
                        if self.manager.get_all_accounts():
                            self.status_var.set("正在自动刷新账号...")
                            self.manager.refresh_all_accounts()
                            self.refresh_account_list()
                            self.status_var.set(f"自动刷新完成 ({datetime.now().strftime('%H:%M:%S')})")
                    except Exception as e:
                        logger.error(f"自动刷新失败: {str(e)}")
                else:
                    time.sleep(1)
        
        threading.Thread(target=auto_refresh_task, daemon=True).start()
    
    def toggle_auto_refresh(self):
        if self.auto_refresh_var.get():
            self.status_var.set("自动刷新已启用")
        else:
            self.status_var.set("自动刷新已禁用")


if __name__ == "__main__":
    root = tk.Tk()
    app = AugmentManagerApp(root)
    root.mainloop()