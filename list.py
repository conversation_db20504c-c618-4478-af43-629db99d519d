import os
import time
import json
import threading
import webbrowser
import pyautogui
import pyperclip
from pynput import mouse, keyboard
from PIL import ImageGrab
import pytesseract
import cv2
import numpy as np
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs

class AugmentVSCodeSwitcher:
    def __init__(self):
        self.status_callback = None
        self.login_url = ""
        self.email = ""
        self.password = ""
        self.session_value = ""
        self.is_running = False
        self.listener = None
        
        # 设置Tesseract OCR路径（如果需要）
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    def set_status_callback(self, callback):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def update_status(self, message):
        """更新状态信息"""
        if self.status_callback:
            self.status_callback(message)
    
    def start_login_process(self, session_value, email, password):
        """启动登录流程"""
        if self.is_running:
            self.update_status("登录流程已在运行中")
            return False
        
        self.session_value = session_value
        self.email = email
        self.password = password
        self.is_running = True
        
        # 启动登录流程线程
        threading.Thread(target=self._login_workflow, daemon=True).start()
        return True
    
    def _login_workflow(self):
        """完整的登录工作流"""
        try:
            self.update_status("步骤1: 获取登录URL...")
            if not self._get_login_url():
                self.update_status("获取登录URL失败")
                return
            
            self.update_status("步骤2: 打开浏览器...")
            webbrowser.open(self.login_url)
            time.sleep(3)  # 等待浏览器打开
            
            self.update_status("步骤3: 填写登录表单...")
            if not self._fill_login_form():
                self.update_status("填写登录表单失败")
                return
            
            self.update_status("步骤4: 获取授权码...")
            auth_code = self._get_auth_code()
            if not auth_code:
                self.update_status("获取授权码失败")
                return
            
            self.update_status("步骤5: 在VSCode中输入授权码...")
            if not self._enter_auth_code_in_vscode(auth_code):
                self.update_status("输入授权码失败")
                return
            
            self.update_status("账号切换成功!")
        except Exception as e:
            self.update_status(f"登录流程出错: {str(e)}")
        finally:
            self.is_running = False
            if self.listener:
                self.listener.stop()
    
    def _get_login_url(self):
        """从session值解析登录URL"""
        try:
            # 解析session值中的用户信息
            parts = self.session_value.split('.')
            if len(parts) < 2:
                return False
            
            # 解码payload
            payload = parts[1]
            # 添加填充以满足base64解码要求
            payload += '=' * (4 - len(payload) % 4
            decoded = base64.urlsafe_b64decode(payload.encode())
            
            try:
                # 尝试解压
                decompressed = zlib.decompress(decoded, -15)
                user_data = json.loads(decompressed)
            except:
                user_data = json.loads(decoded)
            
            user_id = user_data.get("user", {}).get("userId", "")
            tenant_id = user_data.get("user", {}).get("tenantId", "")
            
            # 构造登录URL
            self.login_url = f"https://app.augmentcode.com/login?user_id={user_id}&tenant_id={tenant_id}"
            return True
        except:
            # 如果解析失败，使用默认登录URL
            self.login_url = "https://app.augmentcode.com/login"
            return True
    
    def _fill_login_form(self):
        """自动填写登录表单"""
        try:
            # 定位并点击邮箱输入框
            self.update_status("定位邮箱输入框...")
            email_field = self._find_image_on_screen('email_field.png')
            if not email_field:
                self.update_status("未找到邮箱输入框，尝试默认位置")
                pyautogui.click(800, 400)  # 默认位置
            
            pyautogui.write(self.email)
            time.sleep(0.5)
            
            # 定位并点击密码输入框
            self.update_status("定位密码输入框...")
            password_field = self._find_image_on_screen('password_field.png')
            if not password_field:
                pyautogui.press('tab')
            
            pyautogui.write(self.password)
            time.sleep(0.5)
            
            # 定位并点击登录按钮
            self.update_status("定位登录按钮...")
            login_button = self._find_image_on_screen('login_button.png')
            if login_button:
                pyautogui.click(login_button)
            else:
                # 尝试使用回车键登录
                pyautogui.press('enter')
            
            time.sleep(3)  # 等待登录完成
            return True
        except Exception as e:
            self.update_status(f"填写表单出错: {str(e)}")
            return False
    
    def _get_auth_code(self):
        """获取授权码"""
        try:
            # 监听授权码出现
            self.update_status("等待授权码出现...")
            self.auth_code = ""
            self.listener = mouse.Listener(on_click=self._on_click)
            self.listener.start()
            
            # 设置超时
            start_time = time.time()
            while time.time() - start_time < 60:  # 60秒超时
                if self.auth_code:
                    return self.auth_code
                time.sleep(1)
            
            return None
        except Exception as e:
            self.update_status(f"获取授权码出错: {str(e)}")
            return None
    
    def _on_click(self, x, y, button, pressed):
        """鼠标点击事件处理"""
        if pressed:
            # 截图并识别授权码
            screenshot = ImageGrab.grab(bbox=(x-100, y-50, x+200, y+50))
            
            # 使用OCR识别文本
            text = pytesseract.image_to_string(screenshot)
            
            # 查找6位数字授权码
            code_match = re.search(r'\b\d{6}\b', text)
            if code_match:
                self.auth_code = code_match.group(0)
                self.update_status(f"找到授权码: {self.auth_code}")
                return False  # 停止监听
        
        return True
    
    def _enter_auth_code_in_vscode(self, auth_code):
        """在VSCode中输入授权码"""
        try:
            # 激活VSCode窗口
            self.update_status("激活VSCode窗口...")
            self._activate_vscode()
            time.sleep(1)
            
            # 定位Augment面板
            self.update_status("定位Augment面板...")
            if not self._find_image_on_screen('augment_icon.png'):
                # 使用快捷键打开Augment面板
                pyautogui.hotkey('ctrl', 'shift', 'p')
                time.sleep(1)
                pyautogui.write('Augment: Show Panel')
                time.sleep(1)
                pyautogui.press('enter')
                time.sleep(2)
            
            # 定位授权码输入框
            self.update_status("定位授权码输入框...")
            code_field = self._find_image_on_screen('auth_code_field.png')
            if code_field:
                pyautogui.click(code_field)
                time.sleep(0.5)
                pyautogui.write(auth_code)
                time.sleep(0.5)
                pyautogui.press('enter')
                time.sleep(3)
                return True
            return False
        except Exception as e:
            self.update_status(f"输入授权码出错: {str(e)}")
            return False
    
    def _activate_vscode(self):
        """激活VSCode窗口"""
        # Windows系统
        if os.name == 'nt':
            import win32gui
            import win32con
            
            def enum_windows_callback(hwnd, extra):
                if "visual studio code" in win32gui.GetWindowText(hwnd).lower():
                    # 确保窗口可见
                    if win32gui.IsWindowVisible(hwnd):
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
            
            win32gui.EnumWindows(enum_windows_callback, None)
        # macOS系统
        elif os.name == 'posix':
            os.system('osascript -e \'tell application "Visual Studio Code" to activate\'')
    
    def _find_image_on_screen(self, template_path, confidence=0.7):
        """在屏幕上查找图像"""
        try:
            # 获取屏幕截图
            screenshot = ImageGrab.grab()
            screenshot_np = np.array(screenshot)
            screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)
            
            # 加载模板图像
            template = cv2.imread(template_path, 0)
            if template is None:
                self.update_status(f"无法加载模板图像: {template_path}")
                return None
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 检查匹配度
            if max_val >= confidence:
                # 计算中心点
                h, w = template.shape
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                return (center_x, center_y)
            return None
        except Exception as e:
            self.update_status(f"图像查找出错: {str(e)}")
            return None

# 在GUI中集成
class AugmentManagerApp:
    # ... 之前的代码 ...
    
    def __init__(self, root):
        # ... 其他初始化 ...
        self.switcher = AugmentVSCodeSwitcher()
        self.switcher.set_status_callback(self.update_switcher_status)
        
        # 添加账号凭证存储
        self.account_credentials = {}  # email: {'password': 'xxx'}
    
    def update_switcher_status(self, message):
        """更新切换器状态"""
        self.status_var.set(message)
        self.root.update()
    
    def apply_to_vscode(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if account:
            # 检查是否存储了密码
            if email not in self.account_credentials:
                self.prompt_for_credentials(email)
                return
            
            # 获取凭证
            password = self.account_credentials[email].get('password', '')
            
            if not password:
                messagebox.showwarning("警告", "该账号未设置密码")
                return
            
            # 启动切换流程
            self.status_var.set(f"开始切换到账号: {email}")
            self.switcher.start_login_process(
                account["session"],
                email,
                password
            )
        else:
            self.status_var.set("应用账号失败")
    
    def prompt_for_credentials(self, email):
        """提示输入账号凭证"""
        dialog = tk.Toplevel(self.root)
        dialog.title("输入账号凭证")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text=f"为 {email} 输入密码:").pack(pady=10)
        
        password_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=password_var, show="*", width=30).pack(pady=5)
        
        save_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(dialog, text="保存密码", variable=save_var).pack(pady=5)
        
        def on_submit():
            password = password_var.get()
            if not password:
                messagebox.showerror("错误", "密码不能为空")
                return
            
            if save_var.get():
                self.account_credentials[email] = {'password': password}
            
            dialog.destroy()
            # 重新尝试应用
            self.apply_to_vscode()
        
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=10)
        
        ttk.Button(btn_frame, text="确定", command=on_submit).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)