import os
import json
import sqlite3
import base64
import shutil
import logging
import win32crypt  # Windows only
from cryptography.fernet import <PERSON><PERSON><PERSON>
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("augment_manager.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AugmentAccountManager")

class VSCodeSessionManager:
    def __init__(self):
        self.system = os.name
        self.vscode_path = self.find_vscode_path()
        self.state_db_path = self.find_state_db_path()
        self.encryption_key = None
        
    def find_vscode_path(self):
        """查找 VSCode 安装路径"""
        # Windows
        if self.system == 'nt':
            paths = [
                os.path.expandvars(r"%APPDATA%\Code"),
                os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code")
            ]
            for path in paths:
                if os.path.exists(path):
                    return path
        
        # macOS
        elif self.system == 'posix' and os.uname().sysname == 'Darwin':
            paths = [
                os.path.expanduser("~/Library/Application Support/Code"),
                "/Applications/Visual Studio Code.app/Contents/Resources/app"
            ]
            for path in paths:
                if os.path.exists(path):
                    return path
        
        # Linux
        else:
            paths = [
                os.path.expanduser("~/.config/Code"),
                "/usr/share/code"
            ]
            for path in paths:
                if os.path.exists(path):
                    return path
        
        return None
    
    def find_state_db_path(self):
        """查找 state.vscdb 文件路径"""
        if not self.vscode_path:
            return None
        
        # Windows
        if self.system == 'nt':
            return os.path.join(self.vscode_path, "User", "globalStorage", "state.vscdb")
        
        # macOS
        elif self.system == 'posix' and os.uname().sysname == 'Darwin':
            return os.path.join(self.vscode_path, "User", "globalStorage", "state.vscdb")
        
        # Linux
        else:
            return os.path.join(self.vscode_path, "User", "globalStorage", "state.vscdb")
    
    def backup_state_db(self):
        """备份 state.vscdb 文件"""
        if not self.state_db_path or not os.path.exists(self.state_db_path):
            return False
        
        backup_dir = os.path.join(os.path.dirname(self.state_db_path), "backups")
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"state_backup_{timestamp}.vscdb")
        
        shutil.copy2(self.state_db_path, backup_path)
        return backup_path
    
    def get_encryption_key(self):
        """获取 VSCode 加密密钥"""
        # Windows 使用 DPAPI
        if self.system == 'nt':
            return win32crypt.CryptProtectData(b"AugmentManager", None, None, None, None, 0)
        
        # macOS/Linux - 尝试从密钥链获取或生成固定密钥
        # 注意：实际应用中应使用更安全的密钥管理方式
        return Fernet.generate_key()
    
    def encrypt_session(self, session_value):
        """加密会话数据"""
        if self.system == 'nt':
            # Windows DPAPI 加密
            return win32crypt.CryptProtectData(session_value.encode(), None, None, None, None, 0)
        
        else:
            # AES 加密 (macOS/Linux)
            cipher = AES.new(self.encryption_key, AES.MODE_CBC)
            ct_bytes = cipher.encrypt(pad(session_value.encode(), AES.block_size))
            iv = cipher.iv
            return base64.b64encode(iv + ct_bytes).decode()
    
    def decrypt_session(self, encrypted_data):
        """解密会话数据"""
        if self.system == 'nt':
            # Windows DPAPI 解密
            return win32crypt.CryptUnprotectData(encrypted_data, None, None, None, 0)[1].decode()
        
        else:
            # AES 解密 (macOS/Linux)
            data = base64.b64decode(encrypted_data)
            iv = data[:AES.block_size]
            ct = data[AES.block_size:]
            cipher = AES.new(self.encryption_key, AES.MODE_CBC, iv)
            pt = unpad(cipher.decrypt(ct), AES.block_size)
            return pt.decode()
    
    def update_session(self, session_value):
        """更新数据库中的会话数据"""
        if not self.state_db_path or not os.path.exists(self.state_db_path):
            logger.error("state.vscdb 文件未找到")
            return False
        
        # 备份数据库
        backup_path = self.backup_state_db()
        logger.info(f"已创建数据库备份: {backup_path}")
        
        try:
            # 连接数据库
            conn = sqlite3.connect(self.state_db_path)
            cursor = conn.cursor()
            
            # 加密会话值
            encrypted_session = self.encrypt_session(session_value)
            
            # 更新会话数据
            session_key = 'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}'
            
            # 检查键是否存在
            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (session_key,))
            result = cursor.fetchone()
            
            if result:
                # 更新现有记录
                cursor.execute(
                    "UPDATE ItemTable SET value = ? WHERE key = ?",
                    (encrypted_session, session_key)
            else:
                # 插入新记录
                cursor.execute(
                    "INSERT INTO ItemTable (key, value) VALUES (?, ?)",
                    (session_key, encrypted_session))
            
            # 更新认证状态
            auth_key = "Augment.vscode-augment"
            auth_value = json.dumps({
                "actionSystemStates": {
                    "authenticated": "complete"
                }
            })
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (auth_key,))
            auth_result = cursor.fetchone()
            
            if auth_result:
                # 更新现有记录
                cursor.execute(
                    "UPDATE ItemTable SET value = ? WHERE key = ?",
                    (auth_value, auth_key))
            else:
                # 插入新记录
                cursor.execute(
                    "INSERT INTO ItemTable (key, value) VALUES (?, ?)",
                    (auth_key, auth_value))
            
            # 提交更改
            conn.commit()
            logger.info("数据库更新成功")
            return True
            
        except Exception as e:
            logger.error(f"数据库更新失败: {str(e)}")
            # 恢复备份
            if backup_path and os.path.exists(backup_path):
                shutil.copy2(backup_path, self.state_db_path)
                logger.info("已恢复数据库备份")
            return False
        
        finally:
            if conn:
                conn.close()
    
    def is_vscode_running(self):
        """检查 VSCode 是否正在运行"""
        try:
            # Windows
            if self.system == 'nt':
                import psutil
                for proc in psutil.process_iter(['name']):
                    if "Code.exe" in proc.info['name']:
                        return True
            
            # macOS/Linux
            else:
                import subprocess
                if self.system == 'posix' and os.uname().sysname == 'Darwin':
                    result = subprocess.run(['pgrep', '-x', 'Visual Studio Code'], capture_output=True)
                else:
                    result = subprocess.run(['pgrep', '-x', 'code'], capture_output=True)
                
                return result.returncode == 0
            
            return False
        except:
            return False
    
    def close_vscode(self):
        """关闭 VSCode"""
        try:
            # Windows
            if self.system == 'nt':
                os.system("taskkill /f /im Code.exe")
            
            # macOS
            elif self.system == 'posix' and os.uname().sysname == 'Darwin':
                os.system("pkill -x 'Visual Studio Code'")
            
            # Linux
            else:
                os.system("pkill -x code")
            
            time.sleep(2)  # 等待关闭完成
            return True
        except:
            return False
    
    def restart_vscode(self):
        """重启 VSCode"""
        try:
            # Windows
            if self.system == 'nt':
                os.startfile(os.path.join(self.vscode_path, "Code.exe"))
            
            # macOS
            elif self.system == 'posix' and os.uname().sysname == 'Darwin':
                os.system("open -a 'Visual Studio Code'")
            
            # Linux
            else:
                os.system("code")
            
            return True
        except:
            return False

class AugmentManagerApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Augment 账号管理器")
        self.geometry("1000x700")
        self.vscode_manager = VSCodeSessionManager()
        self.manager = AugmentAccountManager()  # 之前的账号管理器
        
        # 创建UI
        self.create_widgets()
        
        # 加载账号数据
        self.refresh_account_list()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 账号列表
        list_frame = ttk.LabelFrame(main_frame, text="Augment 账号")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=(0, 10), pady=5, expand=False)
        
        # 账号列表表格
        columns = ("#", "邮箱", "计划", "剩余额度", "状态")
        self.account_tree = ttk.Treeview(
            list_frame, 
            columns=columns, 
            show="headings",
            height=15
        )
        
        # 设置列
        self.account_tree.column("#", width=40, anchor=tk.CENTER)
        self.account_tree.column("邮箱", width=200)
        self.account_tree.column("计划", width=150)
        self.account_tree.column("剩余额度", width=100, anchor=tk.CENTER)
        self.account_tree.column("状态", width=80, anchor=tk.CENTER)
        
        for col in columns:
            self.account_tree.heading(col, text=col)
        
        self.account_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.account_tree.bind("<<TreeviewSelect>>", self.on_account_select)
        
        # 账号操作按钮
        btn_frame = ttk.Frame(list_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="添加账号", command=self.add_account).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="刷新账号", command=self.refresh_account).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="删除账号", command=self.delete_account).pack(side=tk.LEFT, padx=2)
        
        # 详情面板
        detail_frame = ttk.LabelFrame(main_frame, text="账号详情")
        detail_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, pady=5)
        
        # 详情文本
        self.detail_text = tk.Text(detail_frame, wrap=tk.WORD, height=15)
        self.detail_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.detail_text.config(state=tk.DISABLED)
        
        # 操作按钮
        action_frame = ttk.Frame(detail_frame)
        action_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(action_frame, text="应用到 VSCode", command=self.apply_to_vscode).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="复制 Session", command=self.copy_session).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="导出账号", command=self.export_accounts).pack(side=tk.LEFT, padx=5)
        
        # 状态栏
        status_frame = ttk.Frame(self)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        # 路径显示
        if self.vscode_manager.state_db_path:
            ttk.Label(
                status_frame, 
                text=f"数据库: {self.vscode_manager.state_db_path}",
                font=("Arial", 8)
            ).pack(side=tk.RIGHT, padx=10)
    
    def refresh_account_list(self):
        # 清空列表
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)
        
        # 添加账号
        accounts = self.manager.get_all_accounts()
        for i, acc in enumerate(accounts):
            status = "正常" if not acc["billing"]["is_expired"] else "已过期"
            remaining = f"{acc['quota']['remaining']}/{acc['quota']['total']}"
            
            self.account_tree.insert("", tk.END, values=(
                i+1, 
                acc["email"], 
                acc["plan_name"], 
                remaining, 
                status
            ))
    
    def on_account_select(self, event):
        # 显示选中账号的详情
        selected = self.account_tree.selection()
        if not selected:
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if account:
            self.show_account_details(account)
    
    def show_account_details(self, account):
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        
        details = f"邮箱: {account['email']}\n"
        details += f"计划: {account['plan_name']} ({account['plan_type']})\n"
        details += f"额度: {account['quota']['used']}/{account['quota']['total']} {account['quota']['unit']}\n"
        details += f"剩余: {account['quota']['remaining']} {account['quota']['unit']}\n"
        details += f"账单周期结束: {account['billing']['period_end']}\n"
        details += f"状态: {'已过期' if account['billing']['is_expired'] else '正常'}\n"
        details += f"最后更新: {account['last_updated']}\n"
        details += f"备注: {account.get('notes', '无')}\n"
        
        self.detail_text.insert(tk.END, details)
        self.detail_text.config(state=tk.DISABLED)
    
    def add_account(self):
        # 添加账号对话框
        dialog = tk.Toplevel(self)
        dialog.title("添加新账号")
        dialog.geometry("500x300")
        
        ttk.Label(dialog, text="Session 值:").pack(pady=(10, 5))
        session_entry = tk.Text(dialog, height=5, width=50)
        session_entry.pack(padx=10, pady=5)
        
        ttk.Label(dialog, text="备注:").pack(pady=(10, 5))
        notes_entry = ttk.Entry(dialog, width=50)
        notes_entry.pack(padx=10, pady=5)
        
        def on_submit():
            session = session_entry.get("1.0", tk.END).strip()
            notes = notes_entry.get().strip()
            
            if not session:
                messagebox.showerror("错误", "Session 值不能为空")
                return
            
            self.status_var.set("正在添加账号...")
            try:
                account = self.manager.add_account(session, notes)
                if account:
                    self.refresh_account_list()
                    self.status_var.set(f"账号添加成功: {account['email']}")
                    dialog.destroy()
                else:
                    self.status_var.set("账号添加失败")
            except Exception as e:
                messagebox.showerror("错误", f"添加账号失败: {str(e)}")
                self.status_var.set(f"添加失败: {str(e)}")
        
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=10)
        
        ttk.Button(btn_frame, text="添加", command=on_submit).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)
    
    def apply_to_vscode(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if not account:
            self.status_var.set("应用账号失败")
            return
        
        # 检查 VSCode 是否运行
        if self.vscode_manager.is_vscode_running():
            if not messagebox.askyesno("确认", 
                    "VSCode 正在运行，需要关闭才能更新会话。\n是否继续？"):
                return
            
            if not self.vscode_manager.close_vscode():
                messagebox.showerror("错误", "无法关闭 VSCode")
                return
        
        # 更新会话
        self.status_var.set("正在更新 VSCode 会话...")
        try:
            if self.vscode_manager.update_session(account["session"]):
                self.vscode_manager.restart_vscode()
                self.status_var.set(f"账号 {email} 已成功应用到 VSCode")
                messagebox.showinfo("成功", "账号切换完成，VSCode 已重启")
            else:
                messagebox.showerror("错误", "更新会话失败，请查看日志")
                self.status_var.set("更新会话失败")
        except Exception as e:
            messagebox.showerror("错误", f"应用账号失败: {str(e)}")
            self.status_var.set(f"应用失败: {str(e)}")
    
    def copy_session(self):
        selected = self.account_tree.selection()
        if not selected:
            return
        
        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)
        
        if account:
            pyperclip.copy(account["session"])
            self.status_var.set(f"已复制 {email} 的 Session 到剪贴板")
    
    def export_accounts(self):
        filename = filedialog.asksaveasfilename(
            title="导出账号",
            filetypes=[("JSON 文件", "*.json")],
            defaultextension=".json"
        )
        
        if filename:
            if self.manager.export_accounts(filename):
                messagebox.showinfo("成功", f"账号已导出到: {filename}")
            else:
                messagebox.showerror("错误", "导出账号失败")

# 主函数
if __name__ == "__main__":
    app = AugmentManagerApp()
    app.mainloop()